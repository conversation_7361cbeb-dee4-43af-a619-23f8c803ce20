<script lang="ts" setup>
import type { V1ManageUserLearningsLearningDetailIDGetResponseResult } from '@/api/api.model'
import { showToast } from 'vant'
import {
  V1CommonFileUploadDownloadFileIdPost,
  V1ManageUserLearningsIncrementTimePut,
  V1ManageUserLearningsLearningDetailId,
} from '@/api/api.req'
import { MediaViewer } from '@/components/MediaViewer'
import { LearningStatus, LearningStatusOptions } from '@/enums/learning'
import { useAsyncData } from '@/hooks/useAsyncData'
import { getOptionLabel, secondsToHMS } from '@/utils'

const route = useRoute()
const router = useRouter()

const id = route.query.id as string

// 获取学习详情数据
const learningDetailApi = useAsyncData(
  async () => {
    if (!id)
      return null
    const resp = await V1ManageUserLearningsLearningDetailId({
      learningDetailId: id,
    })
    return resp
  },
  null as V1ManageUserLearningsLearningDetailIDGetResponseResult | null,
)

// 计算属性
const learningDetail = computed(() => learningDetailApi.data.value)

const statusText = computed(() => {
  if (!learningDetail.value?.status)
    return ''
  return getOptionLabel(LearningStatusOptions, learningDetail.value.status) || ''
})

// 当前总学习时长（秒）
const currentLearningSeconds = computed(() => {
  const detail = learningDetail.value
  if (!detail)
    return 0

  // 如果正在学习，显示实时的学习时长；否则显示接口返回的时长
  if (isLearning.value) {
    return initialLearningTime.value + accumulatedTime.value
  }
  else {
    // 不在学习状态时，始终显示接口返回的最新时长
    return detail.duration ? Number.parseInt(detail.duration) : 0
  }
})

// 格式化时间为 HH:MM:SS 格式
function formatTimeToHMS(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const durationText = computed(() => {
  const detail = learningDetail.value
  if (!detail)
    return { required: '', learned: '' }

  const requiredSeconds = detail.requireDuration ? Number.parseInt(detail.requireDuration) : 0

  return {
    required: secondsToHMS(requiredSeconds),
    learned: formatTimeToHMS(currentLearningSeconds.value),
  }
})

const currentMaterialIndex = ref(0)

const currentMaterial = computed(() => {
  const materials = learningDetail.value?.materialList || []
  return materials[currentMaterialIndex.value]
})

// 当前材料的文件URL
const currentFileUrl = ref('')

// 转换当前材料数据格式以适配MediaViewer组件
const currentMediaItem = computed(() => {
  const material = currentMaterial.value
  const fileUrl = currentFileUrl.value
  console.log('  - material.fileType:', material?.fileType)

  if (!material)
    return null

  const mediaItem = {
    id: material.fileId || material.id || '',
    name: material.filename || '学习材料',
    url: fileUrl,
    type: material.fileType,
    size: material.fileSize ? Number.parseInt(material.fileSize) : undefined,
    createTime: material.lastUpdateTime ? String(material.lastUpdateTime) : undefined,
  }

  return mediaItem
})

const materialCount = computed(() => {
  return learningDetail.value?.materialList?.length || 0
})

const canGoPrev = computed(() => currentMaterialIndex.value > 0)
const canGoNext = computed(() => currentMaterialIndex.value < materialCount.value - 1)

// 判断当前材料是否为视频
const isCurrentMaterialVideo = computed(() => {
  const material = currentMaterial.value
  if (!material)
    return false

  // 根据文件类型判断
  if (material.fileType === 'video')
    return true

  // 根据文件扩展名判断
  const fileName = material.filename || ''
  const ext = fileName.split('.').pop()?.toLowerCase() || ''
  const videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm', 'm4v']

  return videoTypes.includes(ext)
})

// 学习计时相关状态
const isLearning = ref(false) // 是否正在学习
const displayTimer = ref<number | null>(null) // 显示计时器（每秒更新）
const uploadTimer = ref<number | null>(null) // 上传计时器（每10秒上传）
const accumulatedTime = ref(0) // 累计学习时长（秒）
const initialLearningTime = ref(0) // 开始学习时的初始时长（秒）

// MediaViewer组件引用，用于控制视频播放
const mediaViewerRef = ref()

// 初始化数据
learningDetailApi.load()

// 控制视频播放的函数
function pauseVideo() {
  if (!isCurrentMaterialVideo.value)
    return

  // 通过DOM查询找到视频元素并暂停
  const videoElement = document.querySelector('.media-viewer video') as HTMLVideoElement
  if (videoElement) {
    videoElement.pause()
  }
}

// 自动开始学习（页面加载完成后调用）
function autoStartLearning() {
  if (isLearning.value || !learningDetail.value)
    return

  // 获取当前学习详情中的已学时长作为起始时长
  const currentDuration = learningDetail.value?.duration ? Number.parseInt(learningDetail.value.duration) : 0
  initialLearningTime.value = currentDuration
  accumulatedTime.value = 0 // 本次学习的累计时长

  isLearning.value = true

  // 如果当前是视频，等待视频加载完成后播放
  if (isCurrentMaterialVideo.value) {
    setTimeout(() => {
      const videoElement = document.querySelector('.media-viewer video') as HTMLVideoElement
      if (videoElement) {
        // 监听视频加载完成事件
        const onCanPlay = () => {
          videoElement.play().catch((error) => {
            console.log('视频自动播放失败:', error)
          })
          videoElement.removeEventListener('canplay', onCanPlay)
        }

        if (videoElement.readyState >= 3) {
          // 视频已经可以播放
          videoElement.play().catch((error) => {
            console.log('视频自动播放失败:', error)
          })
        }
        else {
          // 等待视频加载完成
          videoElement.addEventListener('canplay', onCanPlay)
        }
      }
    }, 500)
  }

  // 每秒更新显示时间
  displayTimer.value = window.setInterval(() => {
    accumulatedTime.value += 1 // 每秒增加1秒
  }, 1000) // 1秒间隔

  // 每10秒上传一次学习时长
  uploadTimer.value = window.setInterval(async () => {
    // 计算总的学习时长：初始时长 + 本次累计时长
    const totalLearningTime = initialLearningTime.value + accumulatedTime.value

    try {
      // 调用增加学习时长的API，传递总的学习时长
      await V1ManageUserLearningsIncrementTimePut({
        id,
        incrementTime: totalLearningTime.toString(),
      })

      console.log(`已上传学习时长: ${totalLearningTime}秒 (初始: ${initialLearningTime.value}秒 + 本次: ${accumulatedTime.value}秒)`)
    }
    catch (error) {
      console.error('上传学习时长失败:', error)
      // 失败时不显示toast，避免频繁打扰用户
    }
  }, 10000) // 10秒间隔
}

// 方法
function goPrev() {
  if (canGoPrev.value) {
    currentMaterialIndex.value--
  }
}

function goNext() {
  if (canGoNext.value) {
    currentMaterialIndex.value++
  }
}

function goToExam() {
  if (!learningDetail.value?.examRecordId)
    return

  router.push({
    path: '/exam/detail',
    query: {
      id: learningDetail.value.paperId,
    },
  })
}

// 停止学习计时
async function stopLearningTimer() {
  // 清理显示计时器
  if (displayTimer.value) {
    clearInterval(displayTimer.value)
    displayTimer.value = null
  }

  // 清理上传计时器
  if (uploadTimer.value) {
    clearInterval(uploadTimer.value)
    uploadTimer.value = null
  }

  // 如果有学习时长，先上传最终的时长
  if (accumulatedTime.value > 0) {
    const finalTotalTime = initialLearningTime.value + accumulatedTime.value

    try {
      // 上传最终的学习时长
      await V1ManageUserLearningsIncrementTimePut({
        id,
        incrementTime: finalTotalTime.toString(),
      })

      console.log(`停止学习，最终上传时长: ${finalTotalTime}秒`)

      // 直接更新本地数据，避免重新加载接口造成闪烁
      if (learningDetail.value) {
        learningDetail.value.duration = finalTotalTime.toString()
      }
    }
    catch (error) {
      console.error('上传最终学习时长失败:', error)
    }

    // 显示本次学习时长
    showToast(`本次学习时长: ${formatTimeToHMS(accumulatedTime.value)}`)
  }

  isLearning.value = false

  // 如果当前是视频，自动暂停
  pauseVideo()
}

// 获取文件下载URL
async function getFileDownloadUrl(fileId?: string): Promise<string> {
  if (!fileId)
    return ''

  try {
    const response = await V1CommonFileUploadDownloadFileIdPost({
      fileId,
    })

    // axios拦截器会自动返回result字段，所以response就是文件下载URL
    return response || ''
  }
  catch (error) {
    console.error('获取文件下载地址失败:', error)
    return ''
  }
}

// 重试加载材料
function retryLoadMaterial() {
  showToast('正在重新加载...')
  // 重新获取当前材料的文件URL
  loadCurrentFileUrl()
}

// 加载当前材料的文件URL
async function loadCurrentFileUrl() {
  const material = currentMaterial.value

  if (!material?.fileId) {
    currentFileUrl.value = ''
    return
  }

  try {
    const url = await getFileDownloadUrl(material.fileId)
    currentFileUrl.value = url
  }
  catch (error) {
    console.error('加载文件URL失败:', error)
    currentFileUrl.value = ''
  }
}

// 监听当前材料变化，自动加载文件URL
watch(currentMaterial, (newMaterial) => {
  if (newMaterial?.fileId) {
    loadCurrentFileUrl()
  }
  else {
    currentFileUrl.value = ''
  }
}, { immediate: true })

// 监听学习详情数据加载完成，自动开始学习
watch(learningDetail, (newDetail) => {
  if (newDetail && !isLearning.value) {
    // 延迟一下确保页面渲染完成
    setTimeout(() => {
      autoStartLearning()
    }, 1000)
  }
}, { immediate: true })

// 页面卸载时清理计时器
onUnmounted(() => {
  stopLearningTimer()
})

// 页面离开时也清理计时器
onBeforeRouteLeave(() => {
  stopLearningTimer()
})
</script>

<template>
  <div class="learning-detail-page">
    <VanNavBar title="学习详情" left-text="返回" left-arrow @click-left="router.back()" />

    <div v-if="learningDetailApi.loading.value" class="loading-container">
      <VanLoading type="spinner" />
    </div>

    <div v-else-if="learningDetail" class="page-content">
      <!-- 学习统计卡片 -->
      <CardBox class="statistics-card mb-4">
        <div class="statistics-grid">
          <!-- 学习状态 -->
          <div class="stat-item">
            <div class="stat-icon">
              <SvgIcon name="svg/study-1" size="24" />
            </div>
            <div class="stat-content">
              <div class="stat-label">
                学习状态
              </div>
              <div class="stat-value status-value">
                {{ statusText || '未完成' }}
              </div>
            </div>
          </div>

          <!-- 要求时长 -->
          <div class="stat-item">
            <div class="stat-icon">
              <SvgIcon name="study-2" size="24" />
            </div>
            <div class="stat-content">
              <div class="stat-label">
                要求时长
              </div>
              <div class="stat-value">
                {{ durationText.required }}
              </div>
            </div>
          </div>

          <!-- 已学时长 -->
          <div class="stat-item" :class="{ 'learning-active': isLearning }">
            <div class="stat-icon">
              <SvgIcon name="svg/study-3" size="24" />
            </div>
            <div class="stat-content">
              <div class="stat-label">
                已学时长
              </div>
              <div class="stat-value">
                {{ durationText.learned }}
              </div>
            </div>
          </div>
        </div>
      </CardBox>

      <!-- 考试按钮 -->
      <div v-if="learningDetail.paperName" class="exam-section mb-4">
        <VanButton
          type="primary"
          round
          block
          :disabled="!learningDetail.examRecordId"
          class="exam-button"
          @click="goToExam"
        >
          <template #icon>
            <SvgIcon name="svg/test-paper-icon" size="20" />
          </template>
          <span class="flex-1 text-left">{{ learningDetail.paperName }}</span>
          <div class="exam-arrow">
            去考试 >
          </div>
        </VanButton>
      </div>

      <!-- 材料导航区域 -->
      <CardBox v-if="materialCount > 0" class="navigation-card mb-4">
        <div class="material-navigation">
          <VanButton
            :disabled="!canGoPrev"
            size="small"
            round
            type="default"
            class="nav-button"
            @click="goPrev"
          >
            <VanIcon name="arrow-left" />
          </VanButton>

          <div class="material-counter">
            <span class="current">{{ currentMaterialIndex + 1 }}</span>
            <span class="divider">/</span>
            <span class="total">{{ materialCount }}</span>
          </div>

          <VanButton
            :disabled="!canGoNext"
            size="small"
            round
            type="default"
            class="nav-button"
            @click="goNext"
          >
            <VanIcon name="arrow" />
          </VanButton>
        </div>
      </CardBox>

      <!-- 学习内容标题 -->
      <div v-if="materialCount > 0" class="content-header mb-3">
        <h3 class="content-title">
          学习内容
        </h3>
      </div>

      <!-- 学习内容区域 -->
      <div v-if="currentMediaItem" class="mb-4">
        <MediaViewer
          ref="mediaViewerRef"
          :key="`${currentMaterialIndex}-${currentMediaItem.id}-${currentMediaItem.url}`"
          :media-item="currentMediaItem"
          :show-title="true"
          :allow-download="false"
          @retry="retryLoadMaterial"
        />
      </div>

      <!-- 无学习材料时的提示 -->
      <CardBox v-else-if="learningDetail && materialCount === 0" class="mb-4">
        <div class="empty-materials">
          <VanEmpty description="暂无学习材料" />
        </div>
      </CardBox>

      <!-- 操作按钮 -->
      <div v-if="learningDetail.examRecordId && learningDetail.status === LearningStatus.FINISH" class="action-buttons">
        <!-- 去考试按钮 -->
        <VanButton
          type="primary"
          block
          @click="goToExam"
        >
          去考试
        </VanButton>
      </div>
    </div>

    <div v-else class="error-container">
      <EmptyData />
    </div>
  </div>
</template>

<style lang="less" scoped>
.learning-detail-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.page-content {
  padding: 16px;
  max-width: 100%;
}

// 统计卡片样式
.statistics-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;

  @media (max-width: 375px) {
    gap: 8px;
  }
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 12px 6px;
  border-radius: 8px;
  background: #f8f9fa;
  transition: all 0.3s ease;
  min-height: 100px;

  &.learning-active {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
    border: 1px solid #00996b;

    .stat-value {
      color: #00996b;
      font-weight: bold;
      animation: pulse 2s infinite;
    }

    .stat-icon {
      animation: bounce 1s infinite;
    }
  }

  @media (max-width: 375px) {
    padding: 10px 4px;
    min-height: 90px;
  }
}

.stat-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  margin-bottom: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  @media (max-width: 375px) {
    width: 32px;
    height: 32px;
    margin-bottom: 4px;
  }
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-label {
  font-size: 11px;
  color: #666;
  margin-bottom: 2px;
  line-height: 1.2;

  @media (max-width: 375px) {
    font-size: 10px;
  }
}

.stat-value {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  line-height: 1.2;

  &.status-value {
    color: #ff8800;
  }

  @media (max-width: 375px) {
    font-size: 12px;
  }
}

// 考试按钮样式
.exam-section {
  .exam-button {
    background: linear-gradient(135deg, #00996b 0%, #00b377 100%);
    border: none;
    height: 56px;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 4px 12px rgba(0, 153, 107, 0.3);

    .exam-arrow {
      font-size: 14px;
      opacity: 0.9;
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-2px);
  }
}

// 导航卡片样式
.navigation-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
}

.material-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .nav-button {
    width: 40px;
    height: 40px;
    border: 1px solid #e5e5e5;
    background: white;

    &:disabled {
      opacity: 0.3;
    }
  }

  .material-counter {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 16px;
    font-weight: 500;

    .current {
      color: #00996b;
      font-weight: 600;
    }

    .divider {
      color: #ccc;
    }

    .total {
      color: #666;
    }
  }
}

// 内容标题样式
.content-header {
  .content-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    padding-left: 4px;
  }
}

.empty-materials {
  padding: 20px 0;
  text-align: center;
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background-color: white;
  border-top: 1px solid #f0f0f0;
  z-index: 10;
}
</style>
